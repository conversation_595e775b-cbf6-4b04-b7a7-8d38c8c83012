import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'services/chat_service.dart';
import 'models/chat_model.dart';

/// صفحة اختبار نظام المحادثة
class ChatSystemTestScreen extends StatefulWidget {
  const ChatSystemTestScreen({super.key});

  @override
  State<ChatSystemTestScreen> createState() => _ChatSystemTestScreenState();
}

class _ChatSystemTestScreenState extends State<ChatSystemTestScreen> {
  List<String> _testResults = [];
  bool _isRunningTests = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'اختبار نظام المحادثة',
          style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            ElevatedButton(
              onPressed: _isRunningTests ? null : _runAllTests,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                padding: const EdgeInsets.symmetric(vertical: 16),
                minimumSize: const Size(double.infinity, 50),
              ),
              child: _isRunningTests
                  ? const CircularProgressIndicator(color: Colors.white)
                  : Text(
                      'تشغيل جميع الاختبارات',
                      style: GoogleFonts.cairo(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
            ),
            const SizedBox(height: 20),
            Expanded(
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'نتائج الاختبارات:',
                        style: GoogleFonts.cairo(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 10),
                      if (_testResults.isEmpty)
                        Text(
                          'لم يتم تشغيل أي اختبارات بعد',
                          style: GoogleFonts.cairo(
                            fontSize: 14,
                            color: Colors.grey[600],
                          ),
                        )
                      else
                        ..._testResults.map((result) => Padding(
                              padding: const EdgeInsets.only(bottom: 8),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Icon(
                                    result.startsWith('✅')
                                        ? Icons.check_circle
                                        : Icons.error,
                                    color: result.startsWith('✅')
                                        ? Colors.green
                                        : Colors.red,
                                    size: 20,
                                  ),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Text(
                                      result.substring(2),
                                      style: GoogleFonts.cairo(fontSize: 14),
                                    ),
                                  ),
                                ],
                              ),
                            )),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _runAllTests() async {
    setState(() {
      _isRunningTests = true;
      _testResults.clear();
    });

    await _testChatRoomInitialization();
    await _testChatRoomStream();
    await _testMessageSending();
    await _testPresenceSystem();
    await _testTypingIndicators();

    setState(() {
      _isRunningTests = false;
    });
  }

  Future<void> _testChatRoomInitialization() async {
    try {
      await ChatService.initializeDefaultChatRooms();
      _addTestResult('✅ تهيئة غرف المحادثة الافتراضية');
    } catch (e) {
      _addTestResult('❌ فشل في تهيئة غرف المحادثة: $e');
    }
  }

  Future<void> _testChatRoomStream() async {
    try {
      final stream = ChatService.getChatRoomsStream();
      final chatRooms = await stream.first;
      
      if (chatRooms.isNotEmpty) {
        final generalRoom = chatRooms.firstWhere(
          (room) => room.isGeneral,
          orElse: () => throw Exception('لم يتم العثور على الغرفة العامة'),
        );
        
        _addTestResult('✅ تدفق غرف المحادثة يعمل بشكل صحيح');
        _addTestResult('✅ تم العثور على الغرفة العامة: ${generalRoom.name}');
        _addTestResult('✅ عدد غرف المحادثة: ${chatRooms.length}');
      } else {
        _addTestResult('❌ لا توجد غرف محادثة');
      }
    } catch (e) {
      _addTestResult('❌ فشل في اختبار تدفق غرف المحادثة: $e');
    }
  }

  Future<void> _testMessageSending() async {
    try {
      // محاولة إرسال رسالة اختبار للغرفة العامة
      final success = await ChatService.sendMessage(
        chatRoomId: 'general',
        message: 'رسالة اختبار من نظام الاختبار - ${DateTime.now()}',
      );
      
      if (success) {
        _addTestResult('✅ إرسال الرسائل يعمل بشكل صحيح');
      } else {
        _addTestResult('❌ فشل في إرسال الرسالة');
      }
    } catch (e) {
      _addTestResult('❌ خطأ في اختبار إرسال الرسائل: $e');
    }
  }

  Future<void> _testPresenceSystem() async {
    try {
      await ChatService.updatePresence(true);
      _addTestResult('✅ نظام الحضور يعمل بشكل صحيح');
      
      // اختبار تدفق المستخدمين المتصلين
      final stream = ChatService.getOnlineUsersStream();
      final onlineUsers = await stream.first;
      _addTestResult('✅ تدفق المستخدمين المتصلين يعمل');
      _addTestResult('✅ عدد المستخدمين المتصلين: ${onlineUsers.length}');
    } catch (e) {
      _addTestResult('❌ فشل في اختبار نظام الحضور: $e');
    }
  }

  Future<void> _testTypingIndicators() async {
    try {
      await ChatService.sendTypingIndicator('general', true);
      _addTestResult('✅ إرسال مؤشر الكتابة يعمل');
      
      await ChatService.sendTypingIndicator('general', false);
      _addTestResult('✅ إيقاف مؤشر الكتابة يعمل');
      
      // اختبار تدفق مؤشرات الكتابة
      final stream = ChatService.getTypingIndicatorsStream('general');
      final indicators = await stream.first;
      _addTestResult('✅ تدفق مؤشرات الكتابة يعمل');
      _addTestResult('✅ عدد مؤشرات الكتابة: ${indicators.length}');
    } catch (e) {
      _addTestResult('❌ فشل في اختبار مؤشرات الكتابة: $e');
    }
  }

  void _addTestResult(String result) {
    setState(() {
      _testResults.add(result);
    });
  }
}

/// دالة مساعدة لاختبار النظام من خارج الواجهة
class ChatSystemTester {
  static Future<Map<String, bool>> runQuickTests() async {
    final results = <String, bool>{};
    
    try {
      // اختبار تهيئة غرف المحادثة
      await ChatService.initializeDefaultChatRooms();
      results['chatRoomInitialization'] = true;
    } catch (e) {
      results['chatRoomInitialization'] = false;
      print('خطأ في تهيئة غرف المحادثة: $e');
    }
    
    try {
      // اختبار تدفق غرف المحادثة
      final stream = ChatService.getChatRoomsStream();
      final chatRooms = await stream.first.timeout(const Duration(seconds: 5));
      results['chatRoomStream'] = chatRooms.isNotEmpty;
    } catch (e) {
      results['chatRoomStream'] = false;
      print('خطأ في تدفق غرف المحادثة: $e');
    }
    
    try {
      // اختبار نظام الحضور
      await ChatService.updatePresence(true);
      results['presenceSystem'] = true;
    } catch (e) {
      results['presenceSystem'] = false;
      print('خطأ في نظام الحضور: $e');
    }
    
    return results;
  }
  
  static void printTestResults(Map<String, bool> results) {
    print('\n=== نتائج اختبار نظام المحادثة ===');
    results.forEach((test, passed) {
      final status = passed ? '✅ نجح' : '❌ فشل';
      print('$test: $status');
    });
    
    final passedCount = results.values.where((v) => v).length;
    final totalCount = results.length;
    print('\nالنتيجة الإجمالية: $passedCount/$totalCount اختبارات نجحت');
    
    if (passedCount == totalCount) {
      print('🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام.');
    } else {
      print('⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.');
    }
  }
}

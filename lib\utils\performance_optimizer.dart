import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'logger.dart';

/// مُحسن الأداء للتطبيق
class PerformanceOptimizer {
  static final PerformanceOptimizer _instance =
      PerformanceOptimizer._internal();
  factory PerformanceOptimizer() => _instance;
  PerformanceOptimizer._internal();

  // إعدادات التحسين
  static const int _maxCacheSize = 100;
  static const Duration _cacheExpiry = Duration(minutes: 30);
  static const int _maxRetries = 3;
  static const Duration _retryDelay = Duration(seconds: 2);

  // Cache للبيانات
  final Map<String, _CacheItem> _cache = {};
  final Map<String, Timer> _cacheTimers = {};

  // إحصائيات الأداء
  final Map<String, _PerformanceMetrics> _metrics = {};

  /// تهيئة محسن الأداء
  static Future<void> initialize() async {
    try {
      // تحسين إعدادات Firestore
      await _optimizeFirestore();

      // تحسين الذاكرة
      await _optimizeMemory();

      // تحسين الشبكة
      await _optimizeNetwork();

      AppLogger.success('تم تهيئة محسن الأداء', 'PerformanceOptimizer');
    } catch (e) {
      AppLogger.error('خطأ في تهيئة محسن الأداء', 'PerformanceOptimizer', e);
    }
  }

  /// تحسين إعدادات Firestore
  static Future<void> _optimizeFirestore() async {
    final firestore = FirebaseFirestore.instance;

    // تمكين التخزين المؤقت
    await firestore.enablePersistence();

    // تحسين إعدادات الشبكة
    firestore.settings = const Settings(
      persistenceEnabled: true,
      cacheSizeBytes: Settings.CACHE_SIZE_UNLIMITED,
    );
  }

  /// تحسين الذاكرة
  static Future<void> _optimizeMemory() async {
    // تنظيف الذاكرة بشكل دوري
    Timer.periodic(const Duration(minutes: 5), (timer) {
      _cleanupMemory();
    });
  }

  /// تحسين الشبكة
  static Future<void> _optimizeNetwork() async {
    // تحسين إعدادات الشبكة
    // يمكن إضافة تحسينات إضافية هنا
  }

  /// تنظيف الذاكرة
  static void _cleanupMemory() {
    if (kDebugMode) {
      AppLogger.info('تنظيف الذاكرة...', 'PerformanceOptimizer');
    }

    // إجبار garbage collection
    SystemChannels.platform.invokeMethod('SystemNavigator.pop');
  }

  /// حفظ البيانات في الكاش
  void cacheData(String key, dynamic data) {
    try {
      // إزالة العناصر القديمة إذا تجاوز الحد الأقصى
      if (_cache.length >= _maxCacheSize) {
        _removeOldestCacheItem();
      }

      // حفظ البيانات
      _cache[key] = _CacheItem(data, DateTime.now());

      // إعداد مؤقت لانتهاء الصلاحية
      _cacheTimers[key]?.cancel();
      _cacheTimers[key] = Timer(_cacheExpiry, () {
        _cache.remove(key);
        _cacheTimers.remove(key);
      });

      AppLogger.debug('تم حفظ البيانات في الكاش: $key', 'PerformanceOptimizer');
    } catch (e) {
      AppLogger.error(
        'خطأ في حفظ البيانات في الكاش',
        'PerformanceOptimizer',
        e,
      );
    }
  }

  /// استرجاع البيانات من الكاش
  T? getCachedData<T>(String key) {
    try {
      final item = _cache[key];
      if (item == null) return null;

      // التحقق من انتهاء الصلاحية
      if (DateTime.now().difference(item.timestamp) > _cacheExpiry) {
        _cache.remove(key);
        _cacheTimers[key]?.cancel();
        _cacheTimers.remove(key);
        return null;
      }

      AppLogger.debug(
        'تم استرجاع البيانات من الكاش: $key',
        'PerformanceOptimizer',
      );
      return item.data as T?;
    } catch (e) {
      AppLogger.error(
        'خطأ في استرجاع البيانات من الكاش',
        'PerformanceOptimizer',
        e,
      );
      return null;
    }
  }

  /// إزالة أقدم عنصر من الكاش
  void _removeOldestCacheItem() {
    if (_cache.isEmpty) return;

    String? oldestKey;
    DateTime? oldestTime;

    for (final entry in _cache.entries) {
      if (oldestTime == null || entry.value.timestamp.isBefore(oldestTime)) {
        oldestTime = entry.value.timestamp;
        oldestKey = entry.key;
      }
    }

    if (oldestKey != null) {
      _cache.remove(oldestKey);
      _cacheTimers[oldestKey]?.cancel();
      _cacheTimers.remove(oldestKey);
    }
  }

  /// تنفيذ عملية مع إعادة المحاولة
  static Future<T> executeWithRetry<T>(
    Future<T> Function() operation,
    String operationName,
  ) async {
    int attempts = 0;
    Exception? lastException;

    while (attempts < _maxRetries) {
      try {
        final stopwatch = Stopwatch()..start();
        final result = await operation();
        stopwatch.stop();

        // تسجيل الأداء
        _recordPerformance(operationName, stopwatch.elapsedMilliseconds, true);

        return result;
      } catch (e) {
        attempts++;
        lastException = e is Exception ? e : Exception(e.toString());

        AppLogger.warning(
          'فشلت المحاولة $attempts من $operationName: $e',
          'PerformanceOptimizer',
        );

        if (attempts < _maxRetries) {
          await Future.delayed(_retryDelay * attempts);
        }
      }
    }

    // تسجيل الفشل
    _recordPerformance(operationName, 0, false);

    throw lastException ??
        Exception('فشل في تنفيذ $operationName بعد $_maxRetries محاولات');
  }

  /// تسجيل أداء العملية
  static void _recordPerformance(String operation, int duration, bool success) {
    final instance = PerformanceOptimizer();
    final metrics = instance._metrics[operation] ?? _PerformanceMetrics();

    metrics.totalCalls++;
    if (success) {
      metrics.successfulCalls++;
      metrics.totalDuration += duration;
      metrics.averageDuration = metrics.totalDuration / metrics.successfulCalls;

      if (duration > metrics.maxDuration) {
        metrics.maxDuration = duration;
      }
      if (metrics.minDuration == 0 || duration < metrics.minDuration) {
        metrics.minDuration = duration;
      }
    } else {
      metrics.failedCalls++;
    }

    instance._metrics[operation] = metrics;
  }

  /// الحصول على إحصائيات الأداء
  Map<String, Map<String, dynamic>> getPerformanceStats() {
    final stats = <String, Map<String, dynamic>>{};

    for (final entry in _metrics.entries) {
      final metrics = entry.value;
      stats[entry.key] = {
        'totalCalls': metrics.totalCalls,
        'successfulCalls': metrics.successfulCalls,
        'failedCalls': metrics.failedCalls,
        'successRate':
            metrics.totalCalls > 0
                ? (metrics.successfulCalls / metrics.totalCalls * 100)
                    .toStringAsFixed(2)
                : '0.00',
        'averageDuration': '${metrics.averageDuration.toStringAsFixed(2)} ms',
        'minDuration': '${metrics.minDuration} ms',
        'maxDuration': '${metrics.maxDuration} ms',
      };
    }

    return stats;
  }

  /// طباعة إحصائيات الأداء
  void printPerformanceStats() {
    final stats = getPerformanceStats();

    AppLogger.info('=== إحصائيات الأداء ===', 'PerformanceOptimizer');
    for (final entry in stats.entries) {
      AppLogger.info('${entry.key}:', 'PerformanceOptimizer');
      for (final stat in entry.value.entries) {
        AppLogger.info('  ${stat.key}: ${stat.value}', 'PerformanceOptimizer');
      }
    }
    AppLogger.info('======================', 'PerformanceOptimizer');
  }

  /// تنظيف الكاش
  void clearCache() {
    _cache.clear();
    for (final timer in _cacheTimers.values) {
      timer.cancel();
    }
    _cacheTimers.clear();
    AppLogger.info('تم تنظيف الكاش', 'PerformanceOptimizer');
  }

  /// تنظيف الموارد
  void dispose() {
    clearCache();
    _metrics.clear();
    AppLogger.info('تم تنظيف محسن الأداء', 'PerformanceOptimizer');
  }
}

/// عنصر الكاش
class _CacheItem {
  final dynamic data;
  final DateTime timestamp;

  _CacheItem(this.data, this.timestamp);
}

/// مقاييس الأداء
class _PerformanceMetrics {
  int totalCalls = 0;
  int successfulCalls = 0;
  int failedCalls = 0;
  int totalDuration = 0;
  double averageDuration = 0.0;
  int minDuration = 0;
  int maxDuration = 0;
}

/// مساعد لتحسين الأداء
class PerformanceHelper {
  /// تنفيذ عملية مع تتبع الأداء
  static Future<T> trackPerformance<T>(
    String operationName,
    Future<T> Function() operation,
  ) async {
    return PerformanceOptimizer.executeWithRetry(operation, operationName);
  }

  /// تحسين قائمة طويلة
  static Widget optimizeListView({
    required int itemCount,
    required Widget Function(BuildContext, int) itemBuilder,
    double? itemExtent,
    bool shrinkWrap = false,
  }) {
    return ListView.builder(
      itemCount: itemCount,
      itemBuilder: itemBuilder,
      itemExtent: itemExtent,
      shrinkWrap: shrinkWrap,
      cacheExtent: 500, // تحسين التخزين المؤقت
      physics: const BouncingScrollPhysics(), // تحسين الفيزياء
    );
  }

  /// تحسين الصور
  static Widget optimizeImage({
    required String imageUrl,
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
  }) {
    return Image.network(
      imageUrl,
      width: width,
      height: height,
      fit: fit,
      cacheWidth: width?.toInt(),
      cacheHeight: height?.toInt(),
      loadingBuilder: (context, child, loadingProgress) {
        if (loadingProgress == null) return child;
        return const Center(
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF10B981)),
          ),
        );
      },
      errorBuilder: (context, error, stackTrace) {
        return Container(
          width: width,
          height: height,
          color: Colors.grey[300],
          child: const Icon(Icons.error),
        );
      },
    );
  }
}

// أداة لإصلاح جميع استدعاءات print في المشروع
// هذا ملف مؤقت لإصلاح المشاكل

import 'dart:io';

void main() {
  final projectDir = Directory('lib');
  
  // قائمة الملفات التي تحتاج إصلاح
  final filesToFix = [
    'lib/services/email_link_verification_service.dart',
    'lib/services/firebase_email_link_service.dart', 
    'lib/services/simple_email_service.dart',
    'lib/services/test_data_service.dart',
    'lib/services/working_email_service.dart',
    'lib/test_realtime_simple.dart',
    'lib/utils/realtime_db_tester.dart',
  ];
  
  for (final filePath in filesToFix) {
    final file = File(filePath);
    if (file.existsSync()) {
      print('إصلاح ملف: $filePath');
      fixPrintStatements(file);
    }
  }
  
  print('تم إصلاح جميع الملفات!');
}

void fixPrintStatements(File file) {
  String content = file.readAsStringSync();
  
  // إضافة import للـ logger إذا لم يكن موجوداً
  if (!content.contains("import '../utils/logger.dart'") && 
      !content.contains('import \'../utils/logger.dart\'')) {
    
    // البحث عن آخر import
    final lines = content.split('\n');
    int lastImportIndex = -1;
    
    for (int i = 0; i < lines.length; i++) {
      if (lines[i].startsWith('import ')) {
        lastImportIndex = i;
      }
    }
    
    if (lastImportIndex != -1) {
      lines.insert(lastImportIndex + 1, "import '../utils/logger.dart';");
      content = lines.join('\n');
    }
  }
  
  // استبدال استدعاءات print
  content = content.replaceAllMapped(
    RegExp(r"print\('([^']+)'\);"),
    (match) {
      final message = match.group(1)!;
      
      if (message.startsWith('✅')) {
        return "AppLogger.success('${message.substring(2).trim()}');";
      } else if (message.startsWith('❌')) {
        return "AppLogger.error('${message.substring(2).trim()}');";
      } else if (message.startsWith('⚠️')) {
        return "AppLogger.warning('${message.substring(2).trim()}');";
      } else if (message.startsWith('📧')) {
        return "AppLogger.email('${message.substring(2).trim()}');";
      } else if (message.startsWith('🔄')) {
        return "AppLogger.info('${message.substring(2).trim()}');";
      } else if (message.startsWith('🎯') || message.startsWith('🔗')) {
        return "// ${match.group(0)} // تم تعطيل هذا السطر";
      } else if (message.isEmpty || message.trim().isEmpty) {
        return "// ${match.group(0)} // تم تعطيل هذا السطر";
      } else {
        return "AppLogger.info('$message');";
      }
    },
  );
  
  // استبدال استدعاءات print مع متغيرات
  content = content.replaceAllMapped(
    RegExp(r"print\('([^']*)\$([^']+)'\);"),
    (match) {
      final fullMessage = match.group(0)!;
      return "AppLogger.info('${match.group(1)!}\${${match.group(2)!}}');";
    },
  );
  
  file.writeAsStringSync(content);
}

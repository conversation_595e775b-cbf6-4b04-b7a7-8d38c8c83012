import 'package:cloud_firestore/cloud_firestore.dart';

/// نموذج الإعجاب
class LikeModel {
  final String id;
  final String userId;
  final String userName;
  final String postId;
  final String? commentId;
  final DateTime createdAt;

  LikeModel({
    required this.id,
    required this.userId,
    required this.userName,
    required this.postId,
    this.commentId,
    required this.createdAt,
  });

  factory LikeModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return LikeModel(
      id: doc.id,
      userId: data['userId'] ?? '',
      userName: data['userName'] ?? '',
      postId: data['postId'] ?? '',
      commentId: data['commentId'],
      createdAt: (data['createdAt'] as Timestamp).toDate(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'userName': userName,
      'postId': postId,
      'commentId': commentId,
      'createdAt': Timestamp.fromDate(createdAt),
    };
  }
}

/// نموذج المشاركة
class ShareModel {
  final String id;
  final String userId;
  final String userName;
  final String postId;
  final String? message;
  final DateTime createdAt;

  ShareModel({
    required this.id,
    required this.userId,
    required this.userName,
    required this.postId,
    this.message,
    required this.createdAt,
  });

  factory ShareModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return ShareModel(
      id: doc.id,
      userId: data['userId'] ?? '',
      userName: data['userName'] ?? '',
      postId: data['postId'] ?? '',
      message: data['message'],
      createdAt: (data['createdAt'] as Timestamp).toDate(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'userName': userName,
      'postId': postId,
      'message': message,
      'createdAt': Timestamp.fromDate(createdAt),
    };
  }
}

/// نموذج الإشعار
class NotificationModel {
  final String id;
  final String userId;
  final String type; // like, comment, share, mention
  final String title;
  final String message;
  final String? postId;
  final String? commentId;
  final String? fromUserId;
  final String? fromUserName;
  final bool isRead;
  final DateTime createdAt;

  NotificationModel({
    required this.id,
    required this.userId,
    required this.type,
    required this.title,
    required this.message,
    this.postId,
    this.commentId,
    this.fromUserId,
    this.fromUserName,
    this.isRead = false,
    required this.createdAt,
  });

  factory NotificationModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return NotificationModel(
      id: doc.id,
      userId: data['userId'] ?? '',
      type: data['type'] ?? '',
      title: data['title'] ?? '',
      message: data['message'] ?? '',
      postId: data['postId'],
      commentId: data['commentId'],
      fromUserId: data['fromUserId'],
      fromUserName: data['fromUserName'],
      isRead: data['isRead'] ?? false,
      createdAt: (data['createdAt'] as Timestamp).toDate(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'type': type,
      'title': title,
      'message': message,
      'postId': postId,
      'commentId': commentId,
      'fromUserId': fromUserId,
      'fromUserName': fromUserName,
      'isRead': isRead,
      'createdAt': Timestamp.fromDate(createdAt),
    };
  }

  NotificationModel copyWith({
    String? id,
    String? userId,
    String? type,
    String? title,
    String? message,
    String? postId,
    String? commentId,
    String? fromUserId,
    String? fromUserName,
    bool? isRead,
    DateTime? createdAt,
  }) {
    return NotificationModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      type: type ?? this.type,
      title: title ?? this.title,
      message: message ?? this.message,
      postId: postId ?? this.postId,
      commentId: commentId ?? this.commentId,
      fromUserId: fromUserId ?? this.fromUserId,
      fromUserName: fromUserName ?? this.fromUserName,
      isRead: isRead ?? this.isRead,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}

/// نموذج فئة المنشور
class PostCategoryModel {
  final String id;
  final String name;
  final String description;
  final String icon;
  final String color;
  final bool isActive;

  PostCategoryModel({
    required this.id,
    required this.name,
    required this.description,
    required this.icon,
    required this.color,
    this.isActive = true,
  });

  factory PostCategoryModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return PostCategoryModel(
      id: doc.id,
      name: data['name'] ?? '',
      description: data['description'] ?? '',
      icon: data['icon'] ?? '',
      color: data['color'] ?? '',
      isActive: data['isActive'] ?? true,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'description': description,
      'icon': icon,
      'color': color,
      'isActive': isActive,
    };
  }

  static List<PostCategoryModel> getDefaultCategories() {
    return [
      PostCategoryModel(
        id: 'general',
        name: 'عام',
        description: 'منشورات عامة',
        icon: '💬',
        color: '#6B7280',
      ),
      PostCategoryModel(
        id: 'question',
        name: 'سؤال',
        description: 'أسئلة ومساعدة',
        icon: '❓',
        color: '#3B82F6',
      ),
      PostCategoryModel(
        id: 'announcement',
        name: 'إعلان',
        description: 'إعلانات مهمة',
        icon: '📢',
        color: '#EF4444',
      ),
      PostCategoryModel(
        id: 'study',
        name: 'دراسة',
        description: 'مواد دراسية',
        icon: '📚',
        color: '#10B981',
      ),
      PostCategoryModel(
        id: 'discussion',
        name: 'نقاش',
        description: 'نقاشات أكاديمية',
        icon: '💭',
        color: '#8B5CF6',
      ),
      PostCategoryModel(
        id: 'resource',
        name: 'مصدر',
        description: 'مصادر ومراجع',
        icon: '📖',
        color: '#F59E0B',
      ),
    ];
  }
}

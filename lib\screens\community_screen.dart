import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../providers/theme_provider.dart';
import '../models/post_model.dart';
import '../models/interaction_models.dart';
import '../services/post_service.dart';
import '../widgets/post_widget.dart';
import '../utils/logger.dart';
import 'create_post_screen.dart';

class CommunityScreen extends StatefulWidget {
  const CommunityScreen({super.key});

  @override
  State<CommunityScreen> createState() => _CommunityScreenState();
}

class _CommunityScreenState extends State<CommunityScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  final ScrollController _scrollController = ScrollController();
  final List<PostModel> _posts = [];
  DocumentSnapshot? _lastDocument;
  bool _isLoading = false;
  bool _hasMorePosts = true;
  String _selectedCategory = 'الكل';
  List<PostCategoryModel> _categories = [];

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadCategories();
    _loadPosts();
    _setupScrollListener();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutCubic),
    );

    _animationController.forward();
  }

  void _loadCategories() async {
    final categories = await PostService.getCategories();
    setState(() {
      _categories = [
        PostCategoryModel(
          id: 'all',
          name: 'الكل',
          description: 'جميع المنشورات',
          icon: '📋',
          color: '#6B7280',
        ),
        ...categories,
      ];
    });
  }

  void _setupScrollListener() {
    _scrollController.addListener(() {
      if (_scrollController.position.pixels >=
          _scrollController.position.maxScrollExtent - 200) {
        if (!_isLoading && _hasMorePosts) {
          _loadMorePosts();
        }
      }
    });
  }

  void _loadPosts() {
    setState(() {
      _isLoading = true;
      _posts.clear();
      _lastDocument = null;
      _hasMorePosts = true;
    });

    _loadMorePosts();
  }

  void _loadMorePosts() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final postsStream = PostService.getPostsStream(
        category: _selectedCategory == 'الكل' ? null : _selectedCategory,
        limit: 10,
        lastDocument: _lastDocument,
      );

      postsStream.listen((newPosts) {
        if (mounted) {
          setState(() {
            if (_lastDocument == null) {
              _posts.clear();
            }
            _posts.addAll(newPosts);
            _hasMorePosts = newPosts.length == 10;
            _isLoading = false;
          });
        }
      });
    } catch (e) {
      AppLogger.error('خطأ في تحميل المنشورات', 'CommunityScreen', e);
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Scaffold(
          backgroundColor:
              themeProvider.isDarkMode
                  ? const Color(0xFF0F172A)
                  : const Color(0xFFF8FAFC),
          body: AnimatedBuilder(
            animation: _animationController,
            builder: (context, child) {
              return FadeTransition(
                opacity: _fadeAnimation,
                child: SlideTransition(
                  position: _slideAnimation,
                  child: _buildBody(themeProvider),
                ),
              );
            },
          ),
          floatingActionButton: _buildFloatingActionButton(themeProvider),
        );
      },
    );
  }

  Widget _buildBody(ThemeProvider themeProvider) {
    return SafeArea(
      child: CustomScrollView(
        controller: _scrollController,
        physics: const BouncingScrollPhysics(),
        slivers: [
          _buildHeader(themeProvider),
          _buildCategoryFilter(themeProvider),
          _buildPostsList(themeProvider),
          if (_isLoading) _buildLoadingIndicator(themeProvider),
          const SliverToBoxAdapter(child: SizedBox(height: 100)),
        ],
      ),
    );
  }

  Widget _buildHeader(ThemeProvider themeProvider) {
    return SliverToBoxAdapter(
      child: Container(
        height: 160,
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [Color(0xFF667EEA), Color(0xFF764BA2), Color(0xFF6366F1)],
          ),
          borderRadius: const BorderRadius.only(
            bottomLeft: Radius.circular(24),
            bottomRight: Radius.circular(24),
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'مجتمع Legal 2025',
                          style: GoogleFonts.cairo(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'شارك أفكارك واطرح أسئلتك',
                          style: GoogleFonts.cairo(
                            fontSize: 14,
                            color: Colors.white.withValues(alpha: 0.9),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: const Icon(
                      Icons.people,
                      color: Colors.white,
                      size: 28,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildQuickStat('${_posts.length}', 'منشور'),
                  _buildQuickStat(
                    '${_posts.fold(0, (total, post) => total + post.likes)}',
                    'إعجاب',
                  ),
                  _buildQuickStat(
                    '${_posts.fold(0, (total, post) => total + post.comments.length)}',
                    'تعليق',
                  ),
                  _buildQuickStat(
                    '${_posts.fold(0, (total, post) => total + post.shares)}',
                    'مشاركة',
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildQuickStat(String number, String label) {
    return Column(
      children: [
        Text(
          number,
          style: GoogleFonts.cairo(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        Text(
          label,
          style: GoogleFonts.cairo(
            fontSize: 12,
            color: Colors.white.withValues(alpha: 0.8),
          ),
        ),
      ],
    );
  }

  Widget _buildCategoryFilter(ThemeProvider themeProvider) {
    return SliverToBoxAdapter(
      child: Container(
        height: 60,
        margin: const EdgeInsets.symmetric(vertical: 16),
        child: ListView.builder(
          scrollDirection: Axis.horizontal,
          padding: const EdgeInsets.symmetric(horizontal: 16),
          itemCount: _categories.length,
          itemBuilder: (context, index) {
            final category = _categories[index];
            final isSelected = _selectedCategory == category.name;

            return GestureDetector(
              onTap: () {
                setState(() {
                  _selectedCategory = category.name;
                });
                HapticFeedback.lightImpact();
                _loadPosts();
              },
              child: Container(
                margin: const EdgeInsets.only(left: 12),
                padding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 12,
                ),
                decoration: BoxDecoration(
                  color:
                      isSelected
                          ? const Color(0xFF10B981)
                          : (themeProvider.isDarkMode
                              ? const Color(0xFF1E293B)
                              : Colors.white),
                  borderRadius: BorderRadius.circular(25),
                  border: Border.all(
                    color:
                        isSelected
                            ? const Color(0xFF10B981)
                            : (themeProvider.isDarkMode
                                ? Colors.grey[700]!
                                : Colors.grey[200]!),
                  ),
                  boxShadow:
                      isSelected
                          ? [
                            BoxShadow(
                              color: const Color(
                                0xFF10B981,
                              ).withValues(alpha: 0.3),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ]
                          : null,
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(category.icon, style: const TextStyle(fontSize: 16)),
                    const SizedBox(width: 8),
                    Text(
                      category.name,
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color:
                            isSelected
                                ? Colors.white
                                : (themeProvider.isDarkMode
                                    ? Colors.white
                                    : Colors.black87),
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildPostsList(ThemeProvider themeProvider) {
    if (_posts.isEmpty && !_isLoading) {
      return SliverToBoxAdapter(
        child: Container(
          height: 300,
          margin: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color:
                themeProvider.isDarkMode
                    ? const Color(0xFF1E293B)
                    : Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.post_add,
                size: 64,
                color:
                    themeProvider.isDarkMode
                        ? Colors.grey[600]
                        : Colors.grey[400],
              ),
              const SizedBox(height: 16),
              Text(
                'لا توجد منشورات بعد',
                style: GoogleFonts.cairo(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color:
                      themeProvider.isDarkMode
                          ? Colors.grey[400]
                          : Colors.grey[600],
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'كن أول من يشارك في المجتمع!',
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  color:
                      themeProvider.isDarkMode
                          ? Colors.grey[500]
                          : Colors.grey[500],
                ),
              ),
              const SizedBox(height: 20),
              ElevatedButton.icon(
                onPressed: _navigateToCreatePost,
                icon: const Icon(Icons.add),
                label: Text(
                  'إنشاء منشور',
                  style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF10B981),
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }

    return SliverList(
      delegate: SliverChildBuilderDelegate((context, index) {
        final post = _posts[index];
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
          child: PostWidget(
            post: post,
            onPostUpdated: () {
              // Refresh posts when a post is updated
              _loadPosts();
            },
          ),
        );
      }, childCount: _posts.length),
    );
  }

  Widget _buildLoadingIndicator(ThemeProvider themeProvider) {
    return SliverToBoxAdapter(
      child: Container(
        padding: const EdgeInsets.all(20),
        child: Center(
          child: Column(
            children: [
              CircularProgressIndicator(
                valueColor: const AlwaysStoppedAnimation<Color>(
                  Color(0xFF10B981),
                ),
                strokeWidth: 3,
              ),
              const SizedBox(height: 12),
              Text(
                'جاري تحميل المزيد...',
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  color:
                      themeProvider.isDarkMode
                          ? Colors.grey[400]
                          : Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFloatingActionButton(ThemeProvider themeProvider) {
    return FloatingActionButton.extended(
      onPressed: _navigateToCreatePost,
      backgroundColor: const Color(0xFF10B981),
      foregroundColor: Colors.white,
      elevation: 8,
      icon: const Icon(Icons.add),
      label: Text(
        'منشور جديد',
        style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
      ),
    );
  }

  void _navigateToCreatePost() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const CreatePostScreen()),
    );

    if (result == true) {
      // Refresh posts after creating a new one
      _loadPosts();
    }
  }
}

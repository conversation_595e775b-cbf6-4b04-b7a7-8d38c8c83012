import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import '../services/connectivity_service.dart';

/// شريط عرض حالة الاتصال
class ConnectivityBanner extends StatefulWidget {
  final Widget child;
  final bool showWhenConnected;
  final Duration animationDuration;

  const ConnectivityBanner({
    super.key,
    required this.child,
    this.showWhenConnected = false,
    this.animationDuration = const Duration(milliseconds: 300),
  });

  @override
  State<ConnectivityBanner> createState() => _ConnectivityBannerState();
}

class _ConnectivityBannerState extends State<ConnectivityBanner>
    with TickerProviderStateMixin {
  final ConnectivityService _connectivityService = ConnectivityService();
  late AnimationController _animationController;
  late Animation<double> _slideAnimation;
  late Animation<double> _fadeAnimation;

  bool _isConnected = true;
  ConnectivityResult _connectionType = ConnectivityResult.wifi;
  bool _showBanner = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _setupConnectivityListener();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );

    _slideAnimation = Tween<double>(
      begin: -1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  void _setupConnectivityListener() {
    // الحالة الأولية
    _isConnected = _connectivityService.isConnected;
    _connectionType = _connectivityService.connectionType;
    _updateBannerVisibility();

    // الاستماع للتغييرات
    _connectivityService.connectionStatusStream.listen((isConnected) {
      if (mounted) {
        setState(() {
          _isConnected = isConnected;
          _updateBannerVisibility();
        });
      }
    });

    _connectivityService.connectionTypeStream.listen((connectionType) {
      if (mounted) {
        setState(() {
          _connectionType = connectionType;
        });
      }
    });
  }

  void _updateBannerVisibility() {
    final shouldShow = !_isConnected || (widget.showWhenConnected && _isConnected);
    
    if (shouldShow != _showBanner) {
      _showBanner = shouldShow;
      
      if (_showBanner) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            return Transform.translate(
              offset: Offset(0, _slideAnimation.value * 60),
              child: Opacity(
                opacity: _fadeAnimation.value,
                child: _showBanner ? _buildBanner() : const SizedBox.shrink(),
              ),
            );
          },
        ),
        Expanded(child: widget.child),
      ],
    );
  }

  Widget _buildBanner() {
    final isConnected = _isConnected;
    final backgroundColor = isConnected ? Colors.green : Colors.red;
    final textColor = Colors.white;
    final icon = isConnected ? Icons.wifi : Icons.wifi_off;
    final message = isConnected 
        ? 'متصل بالإنترنت (${_getConnectionTypeName()})'
        : 'لا يوجد اتصال بالإنترنت';

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: backgroundColor,
        boxShadow: [
          BoxShadow(
            color: backgroundColor.withValues(alpha: 0.3),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: SafeArea(
        bottom: false,
        child: Row(
          children: [
            Icon(
              icon,
              color: textColor,
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                message,
                style: GoogleFonts.cairo(
                  color: textColor,
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
                textDirection: TextDirection.rtl,
              ),
            ),
            if (!isConnected) ...[
              const SizedBox(width: 8),
              GestureDetector(
                onTap: _retryConnection,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    'إعادة المحاولة',
                    style: GoogleFonts.cairo(
                      color: textColor,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  String _getConnectionTypeName() {
    switch (_connectionType) {
      case ConnectivityResult.wifi:
        return 'WiFi';
      case ConnectivityResult.mobile:
        return 'بيانات الجوال';
      case ConnectivityResult.ethernet:
        return 'إيثرنت';
      case ConnectivityResult.bluetooth:
        return 'بلوتوث';
      case ConnectivityResult.vpn:
        return 'VPN';
      case ConnectivityResult.other:
        return 'أخرى';
      case ConnectivityResult.none:
        return 'لا يوجد اتصال';
    }
  }

  Future<void> _retryConnection() async {
    await _connectivityService.checkConnection();
  }
}

/// Widget مبسط لعرض حالة الاتصال
class ConnectivityIndicator extends StatefulWidget {
  final double size;
  final bool showText;

  const ConnectivityIndicator({
    super.key,
    this.size = 24,
    this.showText = true,
  });

  @override
  State<ConnectivityIndicator> createState() => _ConnectivityIndicatorState();
}

class _ConnectivityIndicatorState extends State<ConnectivityIndicator> {
  final ConnectivityService _connectivityService = ConnectivityService();
  bool _isConnected = true;
  ConnectivityResult _connectionType = ConnectivityResult.wifi;

  @override
  void initState() {
    super.initState();
    _setupListener();
  }

  void _setupListener() {
    _isConnected = _connectivityService.isConnected;
    _connectionType = _connectivityService.connectionType;

    _connectivityService.connectionStatusStream.listen((isConnected) {
      if (mounted) {
        setState(() {
          _isConnected = isConnected;
        });
      }
    });

    _connectivityService.connectionTypeStream.listen((connectionType) {
      if (mounted) {
        setState(() {
          _connectionType = connectionType;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final color = _isConnected ? Colors.green : Colors.red;
    final icon = _getConnectionIcon();

    if (!widget.showText) {
      return Icon(
        icon,
        color: color,
        size: widget.size,
      );
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          icon,
          color: color,
          size: widget.size,
        ),
        const SizedBox(width: 4),
        Text(
          _getConnectionText(),
          style: GoogleFonts.cairo(
            color: color,
            fontSize: widget.size * 0.6,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  IconData _getConnectionIcon() {
    if (!_isConnected) return Icons.wifi_off;
    
    switch (_connectionType) {
      case ConnectivityResult.wifi:
        return Icons.wifi;
      case ConnectivityResult.mobile:
        return Icons.signal_cellular_4_bar;
      case ConnectivityResult.ethernet:
        return Icons.ethernet;
      case ConnectivityResult.bluetooth:
        return Icons.bluetooth;
      case ConnectivityResult.vpn:
        return Icons.vpn_lock;
      default:
        return Icons.device_unknown;
    }
  }

  String _getConnectionText() {
    if (!_isConnected) return 'غير متصل';
    
    switch (_connectionType) {
      case ConnectivityResult.wifi:
        return 'WiFi';
      case ConnectivityResult.mobile:
        return 'جوال';
      case ConnectivityResult.ethernet:
        return 'إيثرنت';
      case ConnectivityResult.bluetooth:
        return 'بلوتوث';
      case ConnectivityResult.vpn:
        return 'VPN';
      default:
        return 'متصل';
    }
  }
}

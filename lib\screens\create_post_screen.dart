import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';
import 'package:file_picker/file_picker.dart';
import '../providers/theme_provider.dart';
import '../services/post_service.dart';
import '../services/upload_manager.dart';
import '../models/interaction_models.dart';
import '../utils/logger.dart';
import '../widgets/upload_progress_widget.dart';

class CreatePostScreen extends StatefulWidget {
  const CreatePostScreen({super.key});

  @override
  State<CreatePostScreen> createState() => _CreatePostScreenState();
}

class _CreatePostScreenState extends State<CreatePostScreen>
    with TickerProviderStateMixin {
  final TextEditingController _contentController = TextEditingController();
  final TextEditingController _pollQuestionController = TextEditingController();
  final List<TextEditingController> _pollOptionsControllers = [
    TextEditingController(),
    TextEditingController(),
  ];

  bool _isAnonymous = false;
  bool _isPollMode = false;
  bool _isLoading = false;
  String _selectedCategory = 'عام';
  final List<String> _tags = [];
  final List<XFile> _selectedImages = [];
  final List<File> _selectedFiles = [];

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  List<PostCategoryModel> _categories = [];

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadCategories();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutCubic),
    );

    _animationController.forward();
  }

  void _loadCategories() async {
    final categories = await PostService.getCategories();
    setState(() {
      _categories = categories;
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    _contentController.dispose();
    _pollQuestionController.dispose();
    for (var controller in _pollOptionsControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Scaffold(
          backgroundColor:
              themeProvider.isDarkMode
                  ? const Color(0xFF0F172A)
                  : const Color(0xFFF8FAFC),
          appBar: _buildAppBar(themeProvider),
          body: AnimatedBuilder(
            animation: _animationController,
            builder: (context, child) {
              return FadeTransition(
                opacity: _fadeAnimation,
                child: SlideTransition(
                  position: _slideAnimation,
                  child: _buildBody(themeProvider),
                ),
              );
            },
          ),
        );
      },
    );
  }

  PreferredSizeWidget _buildAppBar(ThemeProvider themeProvider) {
    return AppBar(
      elevation: 0,
      backgroundColor:
          themeProvider.isDarkMode ? const Color(0xFF1E293B) : Colors.white,
      leading: IconButton(
        icon: Icon(
          Icons.close,
          color: themeProvider.isDarkMode ? Colors.white : Colors.black87,
        ),
        onPressed: () => Navigator.pop(context),
      ),
      title: Text(
        'إنشاء منشور جديد',
        style: GoogleFonts.cairo(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: themeProvider.isDarkMode ? Colors.white : Colors.black87,
        ),
      ),
      actions: [
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: ElevatedButton(
            onPressed: _isLoading ? null : _publishPost,
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF10B981),
              foregroundColor: Colors.white,
              elevation: 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 20),
            ),
            child:
                _isLoading
                    ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                    : Text(
                      'نشر',
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
          ),
        ),
      ],
    );
  }

  Widget _buildBody(ThemeProvider themeProvider) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildUserInfo(themeProvider),
          const SizedBox(height: 16),
          _buildContentInput(themeProvider),
          const SizedBox(height: 16),
          if (_isPollMode) _buildPollSection(themeProvider),
          if (_selectedImages.isNotEmpty) _buildImagePreview(themeProvider),
          if (_selectedFiles.isNotEmpty) _buildFilePreview(themeProvider),
          const SizedBox(height: 16),
          _buildCategorySelector(themeProvider),
          const SizedBox(height: 16),
          _buildOptionsSection(themeProvider),
          const SizedBox(height: 16),
          _buildActionButtons(themeProvider),
          const SizedBox(height: 100), // مساحة إضافية للتمرير
        ],
      ),
    );
  }

  Widget _buildUserInfo(ThemeProvider themeProvider) {
    return Row(
      children: [
        CircleAvatar(
          radius: 24,
          backgroundColor: const Color(0xFF10B981),
          child: Text(
            _isAnonymous ? '؟' : 'أ',
            style: GoogleFonts.cairo(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                _isAnonymous ? 'مجهول' : 'أنت',
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color:
                      themeProvider.isDarkMode ? Colors.white : Colors.black87,
                ),
              ),
              Text(
                'منشور عام',
                style: GoogleFonts.cairo(
                  fontSize: 12,
                  color:
                      themeProvider.isDarkMode
                          ? Colors.grey[400]
                          : Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
        Switch(
          value: _isAnonymous,
          onChanged: (value) {
            setState(() {
              _isAnonymous = value;
            });
            HapticFeedback.lightImpact();
          },
          activeColor: const Color(0xFF10B981),
        ),
      ],
    );
  }

  Widget _buildContentInput(ThemeProvider themeProvider) {
    return Container(
      decoration: BoxDecoration(
        color:
            themeProvider.isDarkMode ? const Color(0xFF1E293B) : Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color:
              themeProvider.isDarkMode ? Colors.grey[700]! : Colors.grey[200]!,
        ),
      ),
      child: TextField(
        controller: _contentController,
        maxLines: 8,
        textDirection: TextDirection.rtl,
        style: GoogleFonts.cairo(
          fontSize: 16,
          color: themeProvider.isDarkMode ? Colors.white : Colors.black87,
        ),
        decoration: InputDecoration(
          hintText:
              _isPollMode ? 'اكتب سؤال الاستطلاع...' : 'ما الذي تريد مشاركته؟',
          hintStyle: GoogleFonts.cairo(
            color:
                themeProvider.isDarkMode ? Colors.grey[400] : Colors.grey[500],
          ),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.all(16),
        ),
      ),
    );
  }

  Widget _buildPollSection(ThemeProvider themeProvider) {
    return Container(
      margin: const EdgeInsets.only(top: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color:
            themeProvider.isDarkMode ? const Color(0xFF1E293B) : Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: const Color(0xFF10B981).withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.poll, color: Color(0xFF10B981), size: 20),
              const SizedBox(width: 8),
              Text(
                'خيارات الاستطلاع',
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF10B981),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ..._pollOptionsControllers.asMap().entries.map((entry) {
            final index = entry.key;
            final controller = entry.value;
            return Container(
              margin: const EdgeInsets.only(bottom: 12),
              child: TextField(
                controller: controller,
                textDirection: TextDirection.rtl,
                style: GoogleFonts.cairo(
                  color:
                      themeProvider.isDarkMode ? Colors.white : Colors.black87,
                ),
                decoration: InputDecoration(
                  hintText: 'الخيار ${index + 1}',
                  hintStyle: GoogleFonts.cairo(
                    color:
                        themeProvider.isDarkMode
                            ? Colors.grey[400]
                            : Colors.grey[500],
                  ),
                  prefixIcon: Icon(
                    Icons.radio_button_unchecked,
                    color:
                        themeProvider.isDarkMode
                            ? Colors.grey[400]
                            : Colors.grey[500],
                  ),
                  suffixIcon:
                      _pollOptionsControllers.length > 2
                          ? IconButton(
                            icon: Icon(
                              Icons.remove_circle_outline,
                              color: Colors.red[400],
                            ),
                            onPressed: () => _removePollOption(index),
                          )
                          : null,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                      color:
                          themeProvider.isDarkMode
                              ? Colors.grey[600]!
                              : Colors.grey[300]!,
                    ),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                      color:
                          themeProvider.isDarkMode
                              ? Colors.grey[600]!
                              : Colors.grey[300]!,
                    ),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(
                      color: Color(0xFF10B981),
                      width: 2,
                    ),
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                ),
              ),
            );
          }),
          if (_pollOptionsControllers.length < 5)
            TextButton.icon(
              onPressed: _addPollOption,
              icon: const Icon(
                Icons.add_circle_outline,
                color: Color(0xFF10B981),
              ),
              label: Text(
                'إضافة خيار',
                style: GoogleFonts.cairo(
                  color: const Color(0xFF10B981),
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildImagePreview(ThemeProvider themeProvider) {
    return Container(
      height: 120,
      margin: const EdgeInsets.only(top: 16),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: _selectedImages.length,
        itemBuilder: (context, index) {
          return Container(
            width: 120,
            margin: const EdgeInsets.only(right: 8),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              image: DecorationImage(
                image: FileImage(File(_selectedImages[index].path)),
                fit: BoxFit.cover,
              ),
            ),
            child: Stack(
              children: [
                Positioned(
                  top: 8,
                  right: 8,
                  child: GestureDetector(
                    onTap: () => _removeImage(index),
                    child: Container(
                      padding: const EdgeInsets.all(4),
                      decoration: const BoxDecoration(
                        color: Colors.black54,
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.close,
                        color: Colors.white,
                        size: 16,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildFilePreview(ThemeProvider themeProvider) {
    return Container(
      margin: const EdgeInsets.only(top: 16),
      child: Column(
        children:
            _selectedFiles.map((file) {
              final fileName = file.path.split('/').last;
              return Container(
                margin: const EdgeInsets.only(bottom: 8),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color:
                      themeProvider.isDarkMode
                          ? const Color(0xFF1E293B)
                          : Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color:
                        themeProvider.isDarkMode
                            ? Colors.grey[700]!
                            : Colors.grey[200]!,
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      _getFileIcon(fileName),
                      color: const Color(0xFF10B981),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        fileName,
                        style: GoogleFonts.cairo(
                          color:
                              themeProvider.isDarkMode
                                  ? Colors.white
                                  : Colors.black87,
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: () => _removeFile(file),
                      icon: Icon(Icons.close, color: Colors.red[400]),
                    ),
                  ],
                ),
              );
            }).toList(),
      ),
    );
  }

  Widget _buildCategorySelector(ThemeProvider themeProvider) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color:
            themeProvider.isDarkMode ? const Color(0xFF1E293B) : Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color:
              themeProvider.isDarkMode ? Colors.grey[700]! : Colors.grey[200]!,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'الفئة',
            style: GoogleFonts.cairo(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: themeProvider.isDarkMode ? Colors.white : Colors.black87,
            ),
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children:
                _categories.map((category) {
                  final isSelected = _selectedCategory == category.name;
                  return GestureDetector(
                    onTap: () {
                      setState(() {
                        _selectedCategory = category.name;
                      });
                      HapticFeedback.lightImpact();
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                      decoration: BoxDecoration(
                        color:
                            isSelected
                                ? const Color(0xFF10B981)
                                : (themeProvider.isDarkMode
                                    ? Colors.grey[700]
                                    : Colors.grey[100]),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            category.icon,
                            style: const TextStyle(fontSize: 16),
                          ),
                          const SizedBox(width: 6),
                          Text(
                            category.name,
                            style: GoogleFonts.cairo(
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                              color:
                                  isSelected
                                      ? Colors.white
                                      : (themeProvider.isDarkMode
                                          ? Colors.white
                                          : Colors.black87),
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildOptionsSection(ThemeProvider themeProvider) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color:
            themeProvider.isDarkMode ? const Color(0xFF1E293B) : Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color:
              themeProvider.isDarkMode ? Colors.grey[700]! : Colors.grey[200]!,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'خيارات إضافية',
            style: GoogleFonts.cairo(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: themeProvider.isDarkMode ? Colors.white : Colors.black87,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Icon(
                Icons.visibility_off,
                color:
                    themeProvider.isDarkMode
                        ? Colors.grey[400]
                        : Colors.grey[600],
                size: 20,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'نشر مجهول',
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    color:
                        themeProvider.isDarkMode
                            ? Colors.white
                            : Colors.black87,
                  ),
                ),
              ),
              Switch(
                value: _isAnonymous,
                onChanged: (value) {
                  setState(() {
                    _isAnonymous = value;
                  });
                  HapticFeedback.lightImpact();
                },
                activeColor: const Color(0xFF10B981),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(ThemeProvider themeProvider) {
    return Row(
      children: [
        Expanded(
          child: _buildActionButton(
            icon: Icons.image,
            label: 'صورة',
            onTap: _pickImages,
            themeProvider: themeProvider,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildActionButton(
            icon: Icons.attach_file,
            label: 'ملف',
            onTap: _pickFiles,
            themeProvider: themeProvider,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildActionButton(
            icon: Icons.poll,
            label: 'استطلاع',
            onTap: _togglePollMode,
            themeProvider: themeProvider,
            isActive: _isPollMode,
          ),
        ),
      ],
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    required ThemeProvider themeProvider,
    bool isActive = false,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16),
        decoration: BoxDecoration(
          color:
              isActive
                  ? const Color(0xFF10B981).withValues(alpha: 0.1)
                  : (themeProvider.isDarkMode
                      ? const Color(0xFF1E293B)
                      : Colors.white),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color:
                isActive
                    ? const Color(0xFF10B981)
                    : (themeProvider.isDarkMode
                        ? Colors.grey[700]!
                        : Colors.grey[200]!),
          ),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color:
                  isActive
                      ? const Color(0xFF10B981)
                      : (themeProvider.isDarkMode
                          ? Colors.grey[400]
                          : Colors.grey[600]),
              size: 24,
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: GoogleFonts.cairo(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color:
                    isActive
                        ? const Color(0xFF10B981)
                        : (themeProvider.isDarkMode
                            ? Colors.grey[400]
                            : Colors.grey[600]),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // وظائف المساعدة
  void _addPollOption() {
    if (_pollOptionsControllers.length < 5) {
      setState(() {
        _pollOptionsControllers.add(TextEditingController());
      });
    }
  }

  void _removePollOption(int index) {
    if (_pollOptionsControllers.length > 2) {
      setState(() {
        _pollOptionsControllers[index].dispose();
        _pollOptionsControllers.removeAt(index);
      });
    }
  }

  void _togglePollMode() {
    setState(() {
      _isPollMode = !_isPollMode;
    });
    HapticFeedback.lightImpact();
  }

  Future<void> _pickImages() async {
    try {
      final ImagePicker picker = ImagePicker();
      final List<XFile> images = await picker.pickMultiImage();

      if (images.isNotEmpty) {
        setState(() {
          _selectedImages.addAll(images);
        });
      }
    } catch (e) {
      AppLogger.error('خطأ في اختيار الصور', 'CreatePostScreen', e);
    }
  }

  Future<void> _pickFiles() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        allowMultiple: true,
        type: FileType.custom,
        allowedExtensions: ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'],
      );

      if (result != null) {
        setState(() {
          _selectedFiles.addAll(
            result.paths.map((path) => File(path!)).toList(),
          );
        });
      }
    } catch (e) {
      AppLogger.error('خطأ في اختيار الملفات', 'CreatePostScreen', e);
    }
  }

  void _removeImage(int index) {
    setState(() {
      _selectedImages.removeAt(index);
    });
  }

  void _removeFile(File file) {
    setState(() {
      _selectedFiles.remove(file);
    });
  }

  IconData _getFileIcon(String fileName) {
    final extension = fileName.split('.').last.toLowerCase();
    switch (extension) {
      case 'pdf':
        return Icons.picture_as_pdf;
      case 'doc':
      case 'docx':
        return Icons.description;
      case 'xls':
      case 'xlsx':
        return Icons.table_chart;
      case 'ppt':
      case 'pptx':
        return Icons.slideshow;
      default:
        return Icons.insert_drive_file;
    }
  }

  Future<void> _publishPost() async {
    if (_contentController.text.trim().isEmpty) {
      _showSnackBar('يرجى كتابة محتوى المنشور');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      Map<String, dynamic>? pollData;
      if (_isPollMode) {
        final options =
            _pollOptionsControllers
                .map((controller) => controller.text.trim())
                .where((text) => text.isNotEmpty)
                .toList();

        if (options.length < 2) {
          _showSnackBar('يجب إضافة خيارين على الأقل للاستطلاع');
          setState(() {
            _isLoading = false;
          });
          return;
        }

        pollData = {
          'question': _contentController.text.trim(),
          'options': options,
          'votes': {for (var option in options) option: 0},
          'voters': <String>[],
        };
      }

      final postId = await PostService.createPost(
        content: _contentController.text.trim(),
        images: _selectedImages,
        attachments: _selectedFiles,
        pollData: pollData,
        isAnonymous: _isAnonymous,
        category: _selectedCategory,
        tags: _tags,
      );

      if (postId != null) {
        AppLogger.success('تم نشر المنشور بنجاح', 'CreatePostScreen');
        if (mounted) {
          Navigator.pop(context, true);
        }
      } else {
        if (mounted) {
          _showSnackBar('فشل في نشر المنشور');
        }
      }
    } catch (e) {
      AppLogger.error('خطأ في نشر المنشور', 'CreatePostScreen', e);
      _showSnackBar('حدث خطأ أثناء النشر');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message, style: GoogleFonts.cairo()),
        backgroundColor: const Color(0xFF10B981),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }
}

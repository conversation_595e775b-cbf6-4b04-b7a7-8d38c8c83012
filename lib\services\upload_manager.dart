import 'dart:io';
import 'package:flutter/foundation.dart';
import 'file_storage_service.dart';
import '../utils/logger.dart';

/// مدير التحميل مع إدارة الطوابير والتقدم
class UploadManager {
  static final UploadManager _instance = UploadManager._internal();
  factory UploadManager() => _instance;
  UploadManager._internal();

  final Map<String, UploadTask> _activeTasks = {};
  final List<UploadTask> _queuedTasks = [];
  int _maxConcurrentUploads = 3;

  /// إضافة مهمة رفع جديدة
  Future<String> addUploadTask({
    required File file,
    required UploadType type,
    String? customPath,
    Map<String, dynamic>? metadata,
    Function(double)? onProgress,
    Function(String)? onComplete,
    Function(String)? onError,
  }) async {
    final taskId = _generateTaskId();
    final task = UploadTask(
      id: taskId,
      file: file,
      type: type,
      customPath: customPath,
      metadata: metadata,
      onProgress: onProgress,
      onComplete: onComplete,
      onError: onError,
    );

    if (_activeTasks.length < _maxConcurrentUploads) {
      _startUpload(task);
    } else {
      _queuedTasks.add(task);
      AppLogger.info('تم إضافة المهمة للطابور: $taskId', 'UploadManager');
    }

    return taskId;
  }

  /// إلغاء مهمة رفع
  void cancelUpload(String taskId) {
    final task = _activeTasks[taskId];
    if (task != null) {
      task.cancel();
      _activeTasks.remove(taskId);
      _processQueue();
    } else {
      _queuedTasks.removeWhere((task) => task.id == taskId);
    }
  }

  /// الحصول على حالة مهمة الرفع
  UploadTaskStatus? getTaskStatus(String taskId) {
    final task = _activeTasks[taskId];
    return task?.status;
  }

  /// الحصول على تقدم مهمة الرفع
  double? getTaskProgress(String taskId) {
    final task = _activeTasks[taskId];
    return task?.progress;
  }

  /// بدء رفع المهمة
  Future<void> _startUpload(UploadTask task) async {
    _activeTasks[task.id] = task;
    task.status = UploadTaskStatus.uploading;

    try {
      String? downloadUrl;

      switch (task.type) {
        case UploadType.image:
          downloadUrl = await FileStorageService.uploadImage(
            imageFile: task.file,
            category: 'posts',
            customPath: task.customPath,
            onProgress: (progress) {
              task.progress = progress;
              task.onProgress?.call(progress);
            },
          );
          break;

        case UploadType.attachment:
          downloadUrl = await FileStorageService.uploadAttachment(
            file: task.file,
            customPath: task.customPath,
            onProgress: (progress) {
              task.progress = progress;
              task.onProgress?.call(progress);
            },
          );
          break;

        case UploadType.profileImage:
          downloadUrl = await FileStorageService.uploadProfileImage(
            imageFile: task.file,
            onProgress: (progress) {
              task.progress = progress;
              task.onProgress?.call(progress);
            },
          );
          break;

        case UploadType.chatImage:
          downloadUrl = await FileStorageService.uploadImage(
            imageFile: task.file,
            category: 'chat_images',
            customPath: task.customPath,
            onProgress: (progress) {
              task.progress = progress;
              task.onProgress?.call(progress);
            },
          );
          break;
      }

      if (downloadUrl != null) {
        task.status = UploadTaskStatus.completed;
        task.downloadUrl = downloadUrl;
        task.onComplete?.call(downloadUrl);
        AppLogger.success('تم رفع الملف بنجاح: ${task.id}', 'UploadManager');
      } else {
        throw Exception('فشل في رفع الملف');
      }
    } catch (e) {
      task.status = UploadTaskStatus.failed;
      task.error = e.toString();
      task.onError?.call(e.toString());
      AppLogger.error('خطأ في رفع الملف: ${task.id}', 'UploadManager', e);
    } finally {
      _activeTasks.remove(task.id);
      _processQueue();
    }
  }

  /// معالجة طابور المهام
  void _processQueue() {
    while (_queuedTasks.isNotEmpty && _activeTasks.length < _maxConcurrentUploads) {
      final task = _queuedTasks.removeAt(0);
      _startUpload(task);
    }
  }

  /// إنشاء معرف فريد للمهمة
  String _generateTaskId() {
    return 'upload_${DateTime.now().millisecondsSinceEpoch}_${DateTime.now().microsecond}';
  }

  /// الحصول على عدد المهام النشطة
  int get activeTasksCount => _activeTasks.length;

  /// الحصول على عدد المهام في الطابور
  int get queuedTasksCount => _queuedTasks.length;

  /// تعيين الحد الأقصى للرفع المتزامن
  void setMaxConcurrentUploads(int max) {
    _maxConcurrentUploads = max;
    _processQueue();
  }

  /// إلغاء جميع المهام
  void cancelAllTasks() {
    for (final task in _activeTasks.values) {
      task.cancel();
    }
    _activeTasks.clear();
    _queuedTasks.clear();
  }

  /// الحصول على إحصائيات المدير
  Map<String, dynamic> getStats() {
    return {
      'activeTasks': _activeTasks.length,
      'queuedTasks': _queuedTasks.length,
      'maxConcurrent': _maxConcurrentUploads,
      'totalProcessed': _activeTasks.length + _queuedTasks.length,
    };
  }
}

/// نوع الرفع
enum UploadType {
  image,
  attachment,
  profileImage,
  chatImage,
}

/// حالة مهمة الرفع
enum UploadTaskStatus {
  pending,
  uploading,
  completed,
  failed,
  cancelled,
}

/// مهمة رفع
class UploadTask {
  final String id;
  final File file;
  final UploadType type;
  final String? customPath;
  final Map<String, dynamic>? metadata;
  final Function(double)? onProgress;
  final Function(String)? onComplete;
  final Function(String)? onError;

  UploadTaskStatus status = UploadTaskStatus.pending;
  double progress = 0.0;
  String? downloadUrl;
  String? error;
  bool _cancelled = false;

  UploadTask({
    required this.id,
    required this.file,
    required this.type,
    this.customPath,
    this.metadata,
    this.onProgress,
    this.onComplete,
    this.onError,
  });

  void cancel() {
    _cancelled = true;
    status = UploadTaskStatus.cancelled;
  }

  bool get isCancelled => _cancelled;

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'fileName': file.path.split('/').last,
      'type': type.toString(),
      'status': status.toString(),
      'progress': progress,
      'downloadUrl': downloadUrl,
      'error': error,
    };
  }
}

/// مساعد لإدارة الرفع المتعدد
class BatchUploadHelper {
  static Future<List<String>> uploadMultipleFiles({
    required List<File> files,
    required UploadType type,
    Function(int, int, double)? onProgress,
    Function(int, String)? onFileComplete,
    Function(int, String)? onFileError,
  }) async {
    final uploadManager = UploadManager();
    final results = <String>[];
    final tasks = <String>[];

    // إضافة جميع المهام
    for (int i = 0; i < files.length; i++) {
      final taskId = await uploadManager.addUploadTask(
        file: files[i],
        type: type,
        onProgress: (progress) {
          onProgress?.call(i, files.length, progress);
        },
        onComplete: (url) {
          results.add(url);
          onFileComplete?.call(i, url);
        },
        onError: (error) {
          onFileError?.call(i, error);
        },
      );
      tasks.add(taskId);
    }

    // انتظار اكتمال جميع المهام
    while (results.length < files.length) {
      await Future.delayed(const Duration(milliseconds: 100));
      
      // التحقق من المهام الفاشلة
      bool hasFailedTasks = false;
      for (final taskId in tasks) {
        final status = uploadManager.getTaskStatus(taskId);
        if (status == UploadTaskStatus.failed) {
          hasFailedTasks = true;
          break;
        }
      }

      if (hasFailedTasks) {
        break;
      }
    }

    return results;
  }
}
